#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEEM Superpixel 可视化工具
用于可视化由SEEM生成的superpixel标签
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import argparse
from pathlib import Path
import random

# 设置英文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

def load_superpixel_data(json_path, png_path):
    """
    加载superpixel数据
    
    Args:
        json_path: JSON文件路径
        png_path: PNG文件路径
    
    Returns:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
    """
    # 加载JSON数据
    with open(json_path, 'r') as f:
        segments_info = json.load(f)
    
    # 加载PNG掩码
    superpixel_mask = np.array(Image.open(png_path))
    
    return segments_info, superpixel_mask

def generate_random_colors(n_colors):
    """Generate distinct colors using predefined color map"""
    # Predefined color map for semantic segmentation
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }
    
    # Class names mapping
    class_names = {
        0: "noise",
        1: "barrier", 
        2: "bicycle",
        3: "bus",
        4: "car",
        5: "construction_vehicle",
        6: "motorcycle",
        7: "pedestrian",
        8: "traffic_cone",
        9: "trailer",
        10: "truck",
        11: "driveable_surface",
        12: "other_flat",
        13: "sidewalk",
        14: "terrain",
        15: "manmade",
        16: "vegetation"
    }
    
    colors = []
    for i in range(n_colors):
        # Use predefined colors if available, otherwise generate random colors
        if i < len(color_map):
            r, g, b = color_map[i]
            colors.append((r / 255, g / 255, b / 255))
        else:
            print(f'Unknown class: {i}')
            # Fallback to random colors for additional segments
            color = tuple(random.random() for _ in range(3))
            colors.append(color)
    
    return colors, class_names

def visualize_superpixels(segments_info, superpixel_mask, save_path=None, show_legend=True):
    """
    可视化superpixel标签
    
    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        save_path: 保存路径
        show_legend: 是否显示图例
    """
    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 左侧：原始superpixel掩码
    ax1.imshow(superpixel_mask, cmap='tab20')
    ax1.set_title('Superpixel Mask', fontsize=16)
    ax1.axis('off')
    
    # 右侧：彩色可视化
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors, class_names = generate_random_colors(len(segments_info))
    
    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
    
    ax2.imshow(colored_mask)
    ax2.set_title('Colored Superpixel Visualization', fontsize=16)
    ax2.axis('off')
    
    # 添加图例
    if show_legend:
        legend_elements = []
        for i, segment in enumerate(segments_info):
            color = colors[i]
            category_id = segment['category_id']
            class_name = class_names.get(category_id, f"unknown_{category_id}")
            label = f"ID: {segment['id']}, {class_name}, Area: {segment['area']:.0f}"
            legend_elements.append(patches.Patch(color=color, label=label))
        
        ax2.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1), 
                  fontsize=8, framealpha=0.8)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存到: {save_path}")
    
    plt.show()

def visualize_single_superpixel(segments_info, superpixel_mask, segment_id, save_path=None):
    """
    可视化单个superpixel
    
    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        segment_id: 要可视化的segment ID
        save_path: 保存路径
    """
    # 找到对应的segment信息
    segment_info = None
    for seg in segments_info:
        if seg['id'] == segment_id:
            segment_info = seg
            break
    
    if segment_info is None:
        print(f"未找到ID为 {segment_id} 的segment")
        return
    
    # 创建掩码
    mask = (superpixel_mask == segment_id)
    
    # 创建可视化
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # 左侧：原始图像（如果有的话）
    ax1.imshow(superpixel_mask, cmap='gray')
    ax1.imshow(mask, alpha=0.7, cmap='Reds')
    ax1.set_title(f'Superpixel ID: {segment_id}', fontsize=16)
    ax1.axis('off')
    
    # 右侧：统计信息
    ax2.axis('off')
    info_text = f"""
    Superpixel Information:
    
    ID: {segment_info['id']}
    Category ID: {segment_info['category_id']}
    Is Thing: {'Yes' if segment_info['isthing'] else 'No'}
    Area: {segment_info['area']:.0f} pixels
    Pixel Count: {np.sum(mask)}
    
    Mask Statistics:
    Width: {superpixel_mask.shape[1]}
    Height: {superpixel_mask.shape[0]}
    """
    
    ax2.text(0.1, 0.9, info_text, transform=ax2.transAxes, fontsize=12,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"单个superpixel可视化结果已保存到: {save_path}")
    
    plt.show()

def analyze_superpixels(segments_info, superpixel_mask):
    """
    分析superpixel统计信息
    
    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
    """
    print("=== Superpixel Statistics ===")
    print(f"Total superpixel count: {len(segments_info)}")
    
    # 面积统计
    areas = [seg['area'] for seg in segments_info]
    print(f"Area range: {min(areas):.0f} - {max(areas):.0f}")
    print(f"Average area: {np.mean(areas):.0f}")
    print(f"Median area: {np.median(areas):.0f}")
    
    # 类别统计
    category_counts = {}
    for seg in segments_info:
        cat_id = seg['category_id']
        category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
    
    print(f"\nCategory distribution:")
    for cat_id, count in sorted(category_counts.items()):
        print(f"  Category {cat_id}: {count}")
    
    # 物体/非物体统计
    thing_count = sum(1 for seg in segments_info if seg['isthing'])
    stuff_count = len(segments_info) - thing_count
    print(f"\nThing classes: {thing_count}")
    print(f"Stuff classes: {stuff_count}")
    
    # 掩码统计
    unique_ids = np.unique(superpixel_mask)
    print(f"\nUnique IDs in mask: {len(unique_ids)}")
    print(f"Mask dimensions: {superpixel_mask.shape}")

def main():
    parser = argparse.ArgumentParser(description='SEEM Superpixel 可视化工具')
    parser.add_argument('--json_path', type=str, required=True,
                       help='JSON文件路径')
    parser.add_argument('--png_path', type=str, required=True,
                       help='PNG文件路径')
    parser.add_argument('--output_dir', type=str, default='./output',
                       help='输出目录')
    parser.add_argument('--segment_id', type=int, default=None,
                       help='要可视化的特定segment ID')
    parser.add_argument('--no_legend', action='store_true',
                       help='不显示图例')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.json_path):
        print(f"错误: JSON文件不存在: {args.json_path}")
        return
    
    if not os.path.exists(args.png_path):
        print(f"错误: PNG文件不存在: {args.png_path}")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    print("正在加载superpixel数据...")
    segments_info, superpixel_mask = load_superpixel_data(args.json_path, args.png_path)
    
    # 分析数据
    analyze_superpixels(segments_info, superpixel_mask)
    
    # 可视化
    print("\n正在生成可视化...")
    
    # 生成文件名
    base_name = Path(args.json_path).stem
    
    if args.segment_id is not None:
        # 可视化单个superpixel
        output_path = os.path.join(args.output_dir, f"{base_name}_segment_{args.segment_id}.png")
        visualize_single_superpixel(segments_info, superpixel_mask, args.segment_id, output_path)
    else:
        # 可视化所有superpixels
        output_path = os.path.join(args.output_dir, f"{base_name}_visualization.png")
        visualize_superpixels(segments_info, superpixel_mask, output_path, not args.no_legend)
    
    print("可视化完成!")

if __name__ == "__main__":
    main()
