#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEEM Superpixel 批量可视化工具
通过index从pkl文件中读取对应数据进行可视化
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import argparse
from pathlib import Path
import random
import cv2
import torch
import pickle
# from nuscenes.nuscenes import NuScenes

# 设置英文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

def load_point(lidar_path, use_dim=None):
    """Load point cloud from file."""
    if lidar_path.endswith('.bin'):
        points = np.fromfile(lidar_path, dtype=np.float32).reshape(-1, 5)
        if use_dim is not None:
            points = points[:, use_dim]
        else:
            points = points[:, :3]  # x, y, z
        return points
    else:
        raise ValueError(f"Unsupported file format: {lidar_path}")

def load_mask(panoptic_path):
    """Load panoptic mask from file."""
    if panoptic_path.endswith('.npz'):
        data = np.load(panoptic_path)
        pts_semantic_mask = data['pts_semantic_mask']
        pts_instance_mask = data['pts_instance_mask']
        return pts_semantic_mask, pts_instance_mask
    else:
        raise ValueError(f"Unsupported file format: {panoptic_path}")

def load_viewimages(img_files_path):
    """Load multi-view images."""
    imgs = {}
    for cam_id, img_path in img_files_path.items():
        img = cv2.imread(img_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        imgs[cam_id] = img
    return imgs

def load_point_mask_viewimages_by_index(pkl_path, data_root, index, load_pic=True, point_dim=4):
    """
    Load point cloud, panoptic mask, and multi-view images from pkl file by index.
    
    Args:
        pkl_path: Path to pkl file
        data_root: Root directory of dataset
        index: Index of the data to load
        load_pic: whether to load images. If False, only load paths.
        point_dim: Number of dimensions to use for point cloud
    
    Returns:
        dict: Contains loaded data for the specified index
    """
    
    label_map = {
        1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
        9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
        22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
    }
    
    # Load pkl file
    with open(pkl_path, 'rb') as f:
        pklfile = pickle.load(f)
    
    info_list = pklfile['data_list']
    VIEWS = ['CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT', 'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']

    data_prefix = {
        'dataset_path': data_root,
        'lidar_path': data_root+'/samples/LIDAR_TOP',
        'pts_panoptic_mask': data_root+'/panoptic/v1.0-mini',
        'CAM_FRONT': data_root+'/samples/CAM_FRONT',
        'CAM_FRONT_RIGHT': data_root+'/samples/CAM_FRONT_RIGHT',
        'CAM_FRONT_LEFT': data_root+'/samples/CAM_FRONT_LEFT',
        'CAM_BACK': data_root+'/samples/CAM_BACK',
        'CAM_BACK_LEFT': data_root+'/samples/CAM_BACK_LEFT',
        'CAM_BACK_RIGHT': data_root+'/samples/CAM_BACK_RIGHT',
    }

    # Check if index is valid
    if index >= len(info_list) or index < 0:
        raise ValueError(f"Index {index} is out of range. Dataset has {len(info_list)} samples.")
    
    info = info_list[index]
    res = {}
    
    # load lidar points
    lidar_prefix = data_prefix.get('lidar_path')
    lidar_path = info['lidar_points']['lidar_path']
    lidar_path = os.path.join(lidar_prefix, lidar_path)
    try:
        lidar_point = load_point(lidar_path, use_dim=range(point_dim))
        res['lidar_point'] = lidar_point
    except:
        data = np.fromfile(lidar_path, dtype=np.float32)
        data = data.reshape(-1, 5)  # nuscenes data format
        res['lidar_point'] = data[:, :point_dim]

    res['lidar_path'] = lidar_path
        
    # load panoptic mask
    dataset_path = data_prefix.get('dataset_path')
    panoptic_path = info['pts_panoptic_mask_path']
    panoptic_path = os.path.join(dataset_path, panoptic_path)
    res['label_path'] = panoptic_path
    pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)
    # map to 0-19 classes
    pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)
    res['pts_semantic_mask'] = pts_semantic_mask
    res['pts_instance_mask'] = pts_instance_mask
    
    # load multi-view images
    img_files_path = {}
    for cam_id, img_info in info['images'].items():  # add prefix to img_path
        if 'img_path' in img_info:
            if cam_id in data_prefix:
                cam_prefix = data_prefix[cam_id]
            img_info['img_path'] = os.path.join(cam_prefix, img_info['img_path'])
            img_files_path[cam_id] = img_info['img_path']
    
    if load_pic:
        imgs = load_viewimages(img_files_path)
    
    # pack by view
    meta_info = {}
    meta_info['img'] = {}
    meta_info['img_path'] = {}
    
    for view in VIEWS:
        if load_pic:
            meta_info['img'][view] = imgs[view]
        meta_info['img_path'][view] = img_files_path[view]
        
    # load multi-view images meta
    meta_info['lidar2cam'], meta_info['cam2img'] = {}, {}
    for cam_id, img_info in info['images'].items():  # add prefix to img_path
        if 'lidar2cam' in img_info:
            meta_info['lidar2cam'][cam_id] = np.array(img_info['lidar2cam'])
        if 'cam2img' in img_info:
            meta_info['cam2img'][cam_id] = np.array(img_info['cam2img'])

    res['imgs_meta'] = meta_info
    
    # load token
    sample_token = info['token']
    res['sample_token'] = sample_token
    
    return res

def proj_lidar2img(points, lidar2img, img_size=(1600, 900), min_dist=1.0):
    """Project lidar points to image plane."""
    if isinstance(points, np.ndarray):
        points = torch.from_numpy(points).float()
    if isinstance(lidar2img, np.ndarray):
        lidar2img = torch.from_numpy(lidar2img).float()
    
    N = points.shape[0]
    device = points.device
    if N == 0:
        return torch.empty((0, 2), device=device), torch.zeros(0, dtype=torch.bool, device=device)

    points = torch.cat([points, torch.ones(points.shape[0],1).to(points)], dim=1)
    lidar2img = lidar2img.to(points)
    points_img = (lidar2img @ points.T)
    depths = points_img[2, :]
    points_img = points_img / points_img[2]
    img_W, img_H = img_size
    
    mask = torch.ones(depths.shape[0], dtype=torch.bool).to(points.device)
    mask = torch.logical_and(mask, depths > min_dist)
    mask = torch.logical_and(mask, points_img[0, :] > 1)
    mask = torch.logical_and(mask, points_img[0, :] < img_W - 1)
    mask = torch.logical_and(mask, points_img[1, :] > 1)
    mask = torch.logical_and(mask, points_img[1, :] < img_H - 1)

    points_img = points_img[:, mask]
    return points_img[:2, :].T, mask

def get_point_gt_colormap():
    """Get color map for point GT visualization."""
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }
    return color_map

def add_point_gt_to_image(image, lidar_points, semantic_mask, lidar2img, 
                         dot_size=2, alpha=0.8, debug=False):
    """Add point GT visualization to RGB image."""
    if debug:
        print(f"[DEBUG] Adding point GT to image")
        print(f"[DEBUG] Image shape: {image.shape}")
        print(f"[DEBUG] Lidar points shape: {lidar_points.shape}")
        print(f"[DEBUG] Semantic mask shape: {semantic_mask.shape}")
    
    # Project lidar points to image
    img_h, img_w = image.shape[:2]
    points_2d, valid_mask = proj_lidar2img(
        lidar_points, lidar2img, 
        img_size=(img_w, img_h), 
        min_dist=1.0
    )
    
    if debug:
        print(f"[DEBUG] Valid projected points: {valid_mask.sum()}/{len(valid_mask)}")
    
    if valid_mask.sum() == 0:
        if debug:
            print(f"[DEBUG] No valid points to project")
        return image
    
    # Get valid semantic labels
    valid_semantic = semantic_mask[valid_mask.cpu().numpy()]
    
    # Get color map
    color_map = get_point_gt_colormap()
    
    # Create overlay image
    overlay = image.copy().astype(np.float32)
    
    # Convert points to numpy for easier indexing
    points_2d_np = points_2d.cpu().numpy()
    
    # Add colored points
    for i, (point, label) in enumerate(zip(points_2d_np, valid_semantic)):
        x, y = int(point[0]), int(point[1])
        if 0 <= x < img_w and 0 <= y < img_h:
            color = color_map.get(label, [255, 255, 255])  # Default to white
            color = np.array(color) / 255.0  # Normalize to [0, 1]
            
            # Draw a small circle
            cv2.circle(overlay, (x, y), dot_size, color * 255, -1)
    
    # Blend with original image
    result = cv2.addWeighted(image.astype(np.float32), 1-alpha, overlay, alpha, 0)
    result = np.clip(result, 0, 255).astype(np.uint8)
    
    if debug:
        print(f"[DEBUG] Added {len(points_2d_np)} points to image")
    
    return result

def find_superpixel_files(token, superpixel_dir):
    """Find superpixel JSON and PNG files by token."""
    json_path = os.path.join(superpixel_dir, f"{token}.json")
    png_path = os.path.join(superpixel_dir, f"{token}.png")

    if os.path.exists(json_path) and os.path.exists(png_path):
        return json_path, png_path
    else:
        return None, None

def load_superpixel_data(json_path, png_path, debug=False):
    """Load superpixel data from JSON and PNG files."""
    if debug:
        print(f"[DEBUG] Loading superpixel data from {json_path} and {png_path}")

    # Load JSON data
    with open(json_path, 'r') as f:
        segments_info = json.load(f)

    # Load PNG mask
    superpixel_mask = np.array(Image.open(png_path))

    if debug:
        print(f"[DEBUG] Loaded {len(segments_info)} segments")
        print(f"[DEBUG] Superpixel mask shape: {superpixel_mask.shape}")

    return segments_info, superpixel_mask

def generate_distinct_colors(n_colors):
    """Generate distinct colors using predefined color map"""
    # Predefined color map for semantic segmentation
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }

    colors = []
    for i in range(n_colors):
        # Use predefined colors if available, otherwise generate random colors
        if i < len(color_map):
            r, g, b = color_map[i]
            colors.append((r / 255, g / 255, b / 255))
        else:
            print(f'Unknown class: {i}')
            # Fallback to random colors for additional segments
            r = random.random()
            g = random.random()
            b = random.random()
            colors.append((r, g, b))

    return colors

def visualize_batch_comparison(data, segments_info, superpixel_mask, camera_name='CAM_FRONT',
                              add_point_gt=True, save_path=None, debug=False):
    """
    Visualize batch comparison with superpixels and point GT.

    Args:
        data: Loaded data dictionary from load_point_mask_viewimages_by_index
        segments_info: Superpixel segments information
        superpixel_mask: Superpixel mask
        camera_name: Camera name for visualization
        add_point_gt: Whether to add point GT visualization
        save_path: Path to save the visualization
        debug: Whether to enable debug mode
    """
    if debug:
        print(f"[DEBUG] Visualizing batch comparison for camera: {camera_name}")

    # Get image for the specified camera
    if camera_name not in data['imgs_meta']['img']:
        print(f"Error: Camera {camera_name} not found in data")
        return

    original_image = data['imgs_meta']['img'][camera_name]

    # Determine number of plots
    n_plots = 2  # colored superpixels + original image
    if add_point_gt:
        n_plots += 1  # + image with point GT

    if n_plots == 2:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        axes = [ax1, ax2]
    else:  # n_plots == 3
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        axes = [ax1, ax2, ax3]

    current_ax = 0

    # First plot: Colored superpixels
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors = generate_distinct_colors(len(segments_info))

    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
        if debug and i % 10 == 0:
            print(f"Processing segment {i} of {len(segments_info)}...")

    axes[current_ax].imshow(colored_mask)
    axes[current_ax].set_title('Colored Superpixels', fontsize=14)
    axes[current_ax].axis('off')
    current_ax += 1

    # Second plot: Original RGB image
    axes[current_ax].imshow(original_image)
    axes[current_ax].set_title(f'Original RGB Image ({camera_name})', fontsize=14)
    axes[current_ax].axis('off')
    current_ax += 1

    # Third plot: RGB image with point GT (if requested)
    if add_point_gt:
        if camera_name in data['imgs_meta']['lidar2cam'] and camera_name in data['imgs_meta']['cam2img']:
            lidar2cam = data['imgs_meta']['lidar2cam'][camera_name]
            cam2img = data['imgs_meta']['cam2img'][camera_name]
            lidar2img = cam2img @ lidar2cam

            if debug:
                print(f"[DEBUG] Adding point GT for camera: {camera_name}")

            image_with_gt = add_point_gt_to_image(
                original_image,
                data['lidar_point'][:, :3],  # Use only x, y, z
                data['pts_semantic_mask'],
                lidar2img,
                dot_size=2,
                alpha=0.6,
                debug=debug
            )
            axes[current_ax].imshow(image_with_gt)
            axes[current_ax].set_title(f'RGB Image + Point GT ({camera_name})', fontsize=14)
            axes[current_ax].axis('off')
        else:
            print(f"Warning: Camera matrices not found for {camera_name}")

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Batch visualization saved to: {save_path}")

    plt.show()

def main():
    parser = argparse.ArgumentParser(description='SEEM Superpixel 批量可视化工具')
    parser.add_argument('--pkl_path', type=str, required=True,
                       help='包含数据信息的pickle文件路径')
    parser.add_argument('--data_root', type=str, required=True,
                       help='数据集根目录')
    parser.add_argument('--superpixel_dir', type=str, required=True,
                       help='Superpixel文件目录（包含JSON和PNG文件）')
    parser.add_argument('--index', type=int, required=True,
                       help='要可视化的数据索引')
    parser.add_argument('--camera_name', type=str, default='CAM_FRONT',
                       help='相机名称（默认为CAM_FRONT）')
    parser.add_argument('--add_point_gt', action='store_true',
                       help='是否添加点云GT可视化')
    parser.add_argument('--output_dir', type=str, default='./batch_output',
                       help='输出目录')
    parser.add_argument('--debug', action='store_true',
                       help='启用debug模式，显示详细信息')

    args = parser.parse_args()

    # Check if files exist
    if not os.path.exists(args.pkl_path):
        print(f"Error: Pickle file not found: {args.pkl_path}")
        return

    if not os.path.exists(args.data_root):
        print(f"Error: Data root not found: {args.data_root}")
        return

    if not os.path.exists(args.superpixel_dir):
        print(f"Error: Superpixel directory not found: {args.superpixel_dir}")
        return

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load data by index
    print(f"Loading data for index {args.index}...")
    try:
        data = load_point_mask_viewimages_by_index(
            args.pkl_path, args.data_root, args.index,
            load_pic=True, point_dim=4
        )
        print(f"Successfully loaded data for token: {data['sample_token']}")
    except Exception as e:
        print(f"Error loading data: {e}")
        return

    # Find superpixel files by token
    token = data['sample_token']
    json_path, png_path = find_superpixel_files(token, args.superpixel_dir)

    if json_path is None or png_path is None:
        print(f"Error: Superpixel files not found for token {token}")
        print(f"Expected files: {token}.json and {token}.png in {args.superpixel_dir}")
        return

    # Load superpixel data
    print("Loading superpixel data...")
    segments_info, superpixel_mask = load_superpixel_data(json_path, png_path, args.debug)

    # Generate output path
    output_filename = f"batch_vis_index_{args.index}_token_{token}_{args.camera_name}.png"
    save_path = os.path.join(args.output_dir, output_filename)

    # Visualize
    print("Generating visualization...")
    visualize_batch_comparison(
        data, segments_info, superpixel_mask,
        camera_name=args.camera_name,
        add_point_gt=args.add_point_gt,
        save_path=save_path,
        debug=args.debug
    )

    print("Batch visualization completed!")

if __name__ == "__main__":
    main()
