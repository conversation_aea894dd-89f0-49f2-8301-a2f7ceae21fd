#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEEM Superpixel Visualization Tool (English Version)
For visualizing SEEM generated superpixel labels
"""

import os
import json
import numpy as np
import matplotlib
# Set matplotlib backend to Agg for headless environments
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import argparse
from pathlib import Path
import random

# Set English fonts
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Helvetica']
plt.rcParams['axes.unicode_minus'] = False

def load_superpixel_data(json_path, png_path):
    """
    Load superpixel data
    
    Args:
        json_path: JSON file path
        png_path: PNG file path
    
    Returns:
        segments_info: List of segmentation information
        superpixel_mask: Superpixel mask image
    """
    # Load JSON data
    with open(json_path, 'r') as f:
        segments_info = json.load(f)
    
    # Load PNG mask
    superpixel_mask = np.array(Image.open(png_path))
    
    return segments_info, superpixel_mask

def generate_distinct_colors(n_colors):
    """Generate distinct colors using predefined color map"""
    # Predefined color map for semantic segmentation
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }
    
    # Class names mapping
    class_names = {
        0: "noise",
        1: "barrier", 
        2: "bicycle",
        3: "bus",
        4: "car",
        5: "construction_vehicle",
        6: "motorcycle",
        7: "pedestrian",
        8: "traffic_cone",
        9: "trailer",
        10: "truck",
        11: "driveable_surface",
        12: "other_flat",
        13: "sidewalk",
        14: "terrain",
        15: "manmade",
        16: "vegetation"
    }
    
    colors = []
    for i in range(n_colors):
        # Use predefined colors if available, otherwise generate random colors
        if i < len(color_map):
            r, g, b = color_map[i]
            colors.append((r / 255, g / 255, b / 255))
        else:
            print(f'Unknown class: {i}')
            # Fallback to random colors for additional segments
            r = random.random()
            g = random.random()
            b = random.random()
            colors.append((r, g, b))
    
    return colors, class_names

def visualize_superpixels(segments_info, superpixel_mask, save_path=None, show_legend=True):
    """
    Visualize superpixel labels
    
    Args:
        segments_info: List of segmentation information
        superpixel_mask: Superpixel mask image
        save_path: Save path
        show_legend: Whether to show legend
    """
    # Create figure
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Left: Original superpixel mask
    im1 = ax1.imshow(superpixel_mask, cmap='tab20')
    ax1.set_title('Superpixel Mask', fontsize=16)
    ax1.axis('off')
    
    # Add colorbar
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    
    # Right: Colored visualization
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors, class_names = generate_distinct_colors(len(segments_info))
    
    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
    
    ax2.imshow(colored_mask)
    ax2.set_title('Colored Superpixel Visualization', fontsize=16)
    ax2.axis('off')
    
    # Add legend
    if show_legend:
        legend_elements = []
        for i, segment in enumerate(segments_info):
            color = colors[i]
            category_id = segment['category_id']
            class_name = class_names.get(category_id, f"unknown_{category_id}")
            label = f"ID: {segment['id']}, {class_name}, Area: {segment['area']:.0f}"
            legend_elements.append(patches.Patch(color=color, label=label))
        
        ax2.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1), 
                  fontsize=8, framealpha=0.8)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")
    
    plt.close(fig)

def visualize_single_superpixel(segments_info, superpixel_mask, segment_id, save_path=None):
    """
    Visualize a single superpixel
    
    Args:
        segments_info: List of segmentation information
        superpixel_mask: Superpixel mask image
        segment_id: Segment ID to visualize
        save_path: Save path
    """
    # Find corresponding segment info
    segment_info = None
    for seg in segments_info:
        if seg['id'] == segment_id:
            segment_info = seg
            break
    
    if segment_info is None:
        print(f"Segment with ID {segment_id} not found")
        return
    
    # Create mask
    mask = (superpixel_mask == segment_id)
    
    # Create visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # Left: Original mask with highlighted segment
    ax1.imshow(superpixel_mask, cmap='gray')
    ax1.imshow(mask, alpha=0.7, cmap='Reds')
    ax1.set_title(f'Superpixel ID: {segment_id}', fontsize=16)
    ax1.axis('off')
    
    # Right: Statistical information
    ax2.axis('off')
    info_text = f"""
    Superpixel Information:
    
    ID: {segment_info['id']}
    Category ID: {segment_info['category_id']}
    Is Thing: {'Yes' if segment_info['isthing'] else 'No'}
    Area: {segment_info['area']:.0f} pixels
    Pixel Count: {np.sum(mask)}
    
    Mask Statistics:
    Width: {superpixel_mask.shape[1]}
    Height: {superpixel_mask.shape[0]}
    """
    
    ax2.text(0.1, 0.9, info_text, transform=ax2.transAxes, fontsize=12,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Single superpixel visualization saved to: {save_path}")
    
    plt.close(fig)

def create_detailed_analysis(segments_info, superpixel_mask, save_path=None):
    """
    Create detailed analysis charts
    
    Args:
        segments_info: List of segmentation information
        superpixel_mask: Superpixel mask image
        save_path: Save path
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Top-left: Area distribution histogram
    areas = [seg['area'] for seg in segments_info]
    ax1.hist(areas, bins=min(20, len(areas)), alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('Area (pixels)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Superpixel Area Distribution')
    ax1.grid(True, alpha=0.3)
    
    # Top-right: Category distribution pie chart
    category_counts = {}
    for seg in segments_info:
        cat_id = seg['category_id']
        category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
    
    if len(category_counts) <= 10:  # Show top 10 categories
        labels = [f'Category {cat_id}' for cat_id in sorted(category_counts.keys())]
        sizes = list(category_counts.values())
        ax2.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Category Distribution')
    else:
        # If too many categories, show bar chart
        top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        cat_ids = [str(item[0]) for item in top_categories]
        counts = [item[1] for item in top_categories]
        ax2.bar(cat_ids, counts, color='lightcoral')
        ax2.set_xlabel('Category ID')
        ax2.set_ylabel('Count')
        ax2.set_title('Top 10 Category Distribution')
        ax2.tick_params(axis='x', rotation=45)
    
    # Bottom-left: Thing vs Stuff statistics
    thing_count = sum(1 for seg in segments_info if seg['isthing'])
    stuff_count = len(segments_info) - thing_count
    
    labels = ['Thing Classes', 'Stuff Classes']
    sizes = [thing_count, stuff_count]
    colors = ['lightblue', 'lightgreen']
    
    ax3.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    ax3.set_title('Thing Classes vs Stuff Classes')
    
    # Bottom-right: Statistical information table
    ax4.axis('off')
    info_text = f"""
    Statistical Summary:
    
    Total Count: {len(segments_info)}
    Mask Size: {superpixel_mask.shape[1]} × {superpixel_mask.shape[0]}
    Total Area: {sum(areas):,.0f} pixels
    
    Area Statistics:
    Min: {min(areas):,.0f}
    Max: {max(areas):,.0f}
    Mean: {np.mean(areas):,.0f}
    Median: {np.median(areas):,.0f}
    Std: {np.std(areas):,.0f}
    
    Category Count: {len(category_counts)}
    Thing Classes: {thing_count}
    Stuff Classes: {stuff_count}
    """
    
    ax4.text(0.1, 0.9, info_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Detailed analysis saved to: {save_path}")
    
    plt.close(fig)

def analyze_superpixels(segments_info, superpixel_mask):
    """
    Analyze superpixel statistics
    
    Args:
        segments_info: List of segmentation information
        superpixel_mask: Superpixel mask image
    """
    print("=== Superpixel Statistics ===")
    print(f"Total superpixel count: {len(segments_info)}")
    
    # Area statistics
    areas = [seg['area'] for seg in segments_info]
    print(f"Area range: {min(areas):.0f} - {max(areas):.0f}")
    print(f"Average area: {np.mean(areas):.0f}")
    print(f"Median area: {np.median(areas):.0f}")
    
    # Category statistics
    category_counts = {}
    for seg in segments_info:
        cat_id = seg['category_id']
        category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
    
    print(f"\nCategory distribution:")
    for cat_id, count in sorted(category_counts.items()):
        print(f"  Category {cat_id}: {count}")
    
    # Thing vs Stuff statistics
    thing_count = sum(1 for seg in segments_info if seg['isthing'])
    stuff_count = len(segments_info) - thing_count
    print(f"\nThing classes: {thing_count}")
    print(f"Stuff classes: {stuff_count}")
    
    # Mask statistics
    unique_ids = np.unique(superpixel_mask)
    print(f"\nUnique IDs in mask: {len(unique_ids)}")
    print(f"Mask dimensions: {superpixel_mask.shape}")

def main():
    parser = argparse.ArgumentParser(description='SEEM Superpixel Visualization Tool (English)')
    parser.add_argument('--json_path', type=str, required=True,
                       help='JSON file path')
    parser.add_argument('--png_path', type=str, required=True,
                       help='PNG file path')
    parser.add_argument('--output_dir', type=str, default='./output',
                       help='Output directory')
    parser.add_argument('--segment_id', type=int, default=None,
                       help='Specific segment ID to visualize')
    parser.add_argument('--no_legend', action='store_true',
                       help='Do not show legend')
    parser.add_argument('--analysis_only', action='store_true',
                       help='Only show analysis charts, not visualization')
    
    args = parser.parse_args()
    
    # Check if files exist
    if not os.path.exists(args.json_path):
        print(f"Error: JSON file does not exist: {args.json_path}")
        return
    
    if not os.path.exists(args.png_path):
        print(f"Error: PNG file does not exist: {args.png_path}")
        return
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load data
    print("Loading superpixel data...")
    segments_info, superpixel_mask = load_superpixel_data(args.json_path, args.png_path)
    
    # Analyze data
    analyze_superpixels(segments_info, superpixel_mask)
    
    # Generate filename
    base_name = Path(args.json_path).stem
    
    if not args.analysis_only:
        # Visualization
        print("\nGenerating visualization...")
        
        if args.segment_id is not None:
            # Visualize single superpixel
            output_path = os.path.join(args.output_dir, f"{base_name}_segment_{args.segment_id}.png")
            visualize_single_superpixel(segments_info, superpixel_mask, args.segment_id, output_path)
        else:
            # Visualize all superpixels
            output_path = os.path.join(args.output_dir, f"{base_name}_visualization.png")
            visualize_superpixels(segments_info, superpixel_mask, output_path, not args.no_legend)
    
    # Detailed analysis
    print("Generating detailed analysis...")
    analysis_path = os.path.join(args.output_dir, f"{base_name}_analysis.png")
    create_detailed_analysis(segments_info, superpixel_mask, analysis_path)
    
    print("Visualization completed!")

if __name__ == "__main__":
    main()
