#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEEM Superpixel 对比可视化工具
显示原始图像和superpixel标签的对比
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import argparse
from pathlib import Path
import random
import cv2
import torch
import pickle
from nuscenes.nuscenes import NuScenes

# 设置英文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

def find_original_image_path(nusc, token, debug=False):
    """
    通过token和nuscenes_devkit查找原始图像路径

    Args:
        token: 图像token (文件名，不包含扩展名)
        nuscenes_dataroot: nuscenes数据集根目录
        debug: 是否启用debug模式

    Returns:
        original_image_path: 原始图像的完整路径，如果找不到则返回None
    """
    
    if debug:
        print(f"[DEBUG] 正在查找token: {token}")

    # 通过token查找sample_data
    try:
        sample_data = nusc.get("sample_data", token)
        image_path = os.path.join(nuscenes_dataroot, sample_data["filename"])

        if debug:
            print(f"[DEBUG] 找到图像路径: {image_path}")
            print(f"[DEBUG] 文件是否存在: {os.path.exists(image_path)}")

        if os.path.exists(image_path):
            return image_path
        else:
            if debug:
                print(f"[DEBUG] 图像文件不存在: {image_path}")
            return None

    except Exception as e:
        if debug:
            print(f"[DEBUG] 无法找到token {token}: {e}")
        return None

    # except Exception as e:
    #     if debug:
    #         print(f"[DEBUG] 初始化NuScenes失败: {e}")
    #     return None

def proj_lidar2img(points, lidar2img, img_size=(1600, 900), min_dist=1.0):
    """Project lidar points to image plane.
    Args:
        points (torch.Tensor): Lidar points. (N, 3)
        lidar2img (torch.Tensor): Lidar to image matrix. (4, 4)
        img_size (tuple): Image size.
        min_dist (float): Minimum distance to the camera.
    """
    if isinstance(points, np.ndarray):
        points = torch.from_numpy(points).float()
    if isinstance(lidar2img, np.ndarray):
        lidar2img = torch.from_numpy(lidar2img).float()

    N = points.shape[0]
    device = points.device
    if N == 0:
        return torch.empty((0, 2), device=device), torch.zeros(0, dtype=torch.bool, device=device)

    points = torch.cat([points, torch.ones(points.shape[0],1).to(points)], dim=1)
    lidar2img = lidar2img.to(points)
    points_img = (lidar2img @ points.T)
    depths = points_img[2, :]
    points_img = points_img / points_img[2]
    img_W, img_H = img_size

    mask = torch.ones(depths.shape[0], dtype=torch.bool).to(points.device)
    mask = torch.logical_and(mask, depths > min_dist)
    mask = torch.logical_and(mask, points_img[0, :] > 1)
    mask = torch.logical_and(mask, points_img[0, :] < img_W - 1)
    mask = torch.logical_and(mask, points_img[1, :] > 1)
    mask = torch.logical_and(mask, points_img[1, :] < img_H - 1)

    points_img = points_img[:, mask]
    return points_img[:2, :].T, mask

def load_point(lidar_path):
    """Load point cloud from file."""
    if lidar_path.endswith('.bin'):
        points = np.fromfile(lidar_path, dtype=np.float32).reshape(-1, 5)
        return points[:, :3]  # x, y, z
    else:
        raise ValueError(f"Unsupported file format: {lidar_path}")

def load_mask(panoptic_path):
    """Load panoptic mask from file."""
    if panoptic_path.endswith('.npz'):
        data = np.load(panoptic_path)
        pts_semantic_mask = data['pts_semantic_mask']
        pts_instance_mask = data['pts_instance_mask']
        return pts_semantic_mask, pts_instance_mask
    else:
        raise ValueError(f"Unsupported file format: {panoptic_path}")

def load_viewimages(img_files_path):
    """Load multi-view images."""
    imgs = {}
    for cam_id, img_path in img_files_path.items():
        img = cv2.imread(img_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        imgs[cam_id] = img
    return imgs

def get_point_gt_colormap():
    """Get color map for point GT visualization."""
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }
    return color_map

def load_point_cloud_data(pkl_path, data_root, sample_token, debug=False):
    """
    Load point cloud data and GT masks for a specific sample.

    Args:
        pkl_path: Path to the pickle file containing data info
        data_root: Root directory of the dataset
        sample_token: Token of the sample to load
        debug: Whether to enable debug mode

    Returns:
        dict: Contains lidar_points, semantic_mask, and camera matrices
    """
    if debug:
        print(f"[DEBUG] Loading point cloud data from {pkl_path}")
        print(f"[DEBUG] Data root: {data_root}")
        print(f"[DEBUG] Sample token: {sample_token}")

    # Load pickle file
    with open(pkl_path, 'rb') as f:
        pkl_data = pickle.load(f)

    # Label mapping from original to 0-16 classes
    label_map = {
        1: 0, 5: 0, 7: 0, 8: 0, 10: 0, 11: 0, 13: 0, 19: 0, 20: 0, 0: 0, 29: 0, 31: 0,
        9: 1, 14: 2, 15: 3, 16: 3, 17: 4, 18: 5, 21: 6, 2: 7, 3: 7, 4: 7, 6: 7, 12: 8,
        22: 9, 23: 10, 24: 11, 25: 12, 26: 13, 27: 14, 28: 15, 30: 16
    }

    # Find the sample with matching token
    info_list = pkl_data['data_list']
    target_info = None
    for info in info_list:
        if info['token'] == sample_token:
            target_info = info
            break

    if target_info is None:
        if debug:
            print(f"[DEBUG] Sample token {sample_token} not found in pickle file")
        return None

    if debug:
        print(f"[DEBUG] Found matching sample info")

    # Load lidar points
    lidar_prefix = os.path.join(data_root, 'samples/LIDAR_TOP')
    lidar_path = os.path.join(lidar_prefix, target_info['lidar_points']['lidar_path'])
    lidar_points = load_point(lidar_path)

    # Load panoptic mask
    panoptic_path = os.path.join(data_root, target_info['pts_panoptic_mask_path'])
    pts_semantic_mask, pts_instance_mask = load_mask(panoptic_path)

    # Map to 0-16 classes
    pts_semantic_mask = np.vectorize(label_map.get)(pts_semantic_mask)

    # Load camera matrices
    camera_matrices = {}
    for cam_id, img_info in target_info['images'].items():
        if 'lidar2cam' in img_info and 'cam2img' in img_info:
            lidar2cam = np.array(img_info['lidar2cam'])
            cam2img = np.array(img_info['cam2img'])
            lidar2img = cam2img @ lidar2cam
            camera_matrices[cam_id] = lidar2img

    if debug:
        print(f"[DEBUG] Loaded {len(lidar_points)} lidar points")
        print(f"[DEBUG] Semantic mask shape: {pts_semantic_mask.shape}")
        print(f"[DEBUG] Available cameras: {list(camera_matrices.keys())}")

    return {
        'lidar_points': lidar_points,
        'semantic_mask': pts_semantic_mask,
        'instance_mask': pts_instance_mask,
        'camera_matrices': camera_matrices
    }

def add_point_gt_to_image(image, lidar_points, semantic_mask, lidar2img,
                         dot_size=2, alpha=0.8, debug=False):
    """
    Add point GT visualization to RGB image.

    Args:
        image: RGB image (H, W, 3)
        lidar_points: Lidar points (N, 3)
        semantic_mask: Semantic labels for each point (N,)
        lidar2img: Lidar to image transformation matrix (4, 4)
        dot_size: Size of the dots
        alpha: Transparency of the dots
        debug: Whether to enable debug mode

    Returns:
        image_with_points: Image with point GT overlaid
    """
    if debug:
        print(f"[DEBUG] Adding point GT to image")
        print(f"[DEBUG] Image shape: {image.shape}")
        print(f"[DEBUG] Lidar points shape: {lidar_points.shape}")
        print(f"[DEBUG] Semantic mask shape: {semantic_mask.shape}")

    # Project lidar points to image
    img_h, img_w = image.shape[:2]
    points_2d, valid_mask = proj_lidar2img(
        lidar_points, lidar2img,
        img_size=(img_w, img_h),
        min_dist=1.0
    )

    if debug:
        print(f"[DEBUG] Valid projected points: {valid_mask.sum()}/{len(valid_mask)}")

    if valid_mask.sum() == 0:
        if debug:
            print(f"[DEBUG] No valid points to project")
        return image

    # Get valid semantic labels
    valid_semantic = semantic_mask[valid_mask.cpu().numpy()]

    # Get color map
    color_map = get_point_gt_colormap()

    # Create overlay image
    overlay = image.copy().astype(np.float32)

    # Convert points to numpy for easier indexing
    points_2d_np = points_2d.cpu().numpy()

    # Add colored points
    for i, (point, label) in enumerate(zip(points_2d_np, valid_semantic)):
        x, y = int(point[0]), int(point[1])
        if 0 <= x < img_w and 0 <= y < img_h:
            color = color_map.get(label, [255, 255, 255])  # Default to white
            color = np.array(color) / 255.0  # Normalize to [0, 1]

            # Draw a small circle
            cv2.circle(overlay, (x, y), dot_size, color * 255, -1)

    # Blend with original image
    result = cv2.addWeighted(image.astype(np.float32), 1-alpha, overlay, alpha, 0)
    result = np.clip(result, 0, 255).astype(np.uint8)

    if debug:
        print(f"[DEBUG] Added {len(points_2d_np)} points to image")

    return result

def load_data(json_path, png_path, original_image_path=None, debug=False):
    """
    加载所有数据
    
    Args:
        json_path: JSON文件路径
        png_path: PNG文件路径
        original_image_path: 原始图像路径（可选）
        debug: 是否启用debug模式
    
    Returns:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        original_image: 原始图像（如果提供）
    """
    if debug:
        print(f"[DEBUG] 开始加载数据...")
        print(f"[DEBUG] JSON文件: {json_path}")
        print(f"[DEBUG] PNG文件: {png_path}")
        if original_image_path:
            print(f"[DEBUG] 原始图像: {original_image_path}")
    
    # 加载JSON数据
    with open(json_path, 'r') as f:
        segments_info = json.load(f)
    
    if debug:
        print(f"[DEBUG] JSON数据加载完成，包含 {len(segments_info)} 个segment")
        print(f"[DEBUG] 第一个segment示例: {segments_info[0] if segments_info else 'None'}")
    
    # 加载PNG掩码
    superpixel_mask = np.array(Image.open(png_path))
    
    if debug:
        print(f"[DEBUG] PNG掩码加载完成，尺寸: {superpixel_mask.shape}")
        print(f"[DEBUG] 掩码数据类型: {superpixel_mask.dtype}")
        print(f"[DEBUG] 掩码值范围: {superpixel_mask.min()} - {superpixel_mask.max()}")
    
    # 加载原始图像（如果提供）
    original_image = None
    if original_image_path and os.path.exists(original_image_path):
        original_image = cv2.imread(original_image_path)
        original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        
        if debug:
            print(f"[DEBUG] 原始图像加载完成，尺寸: {original_image.shape}")
            print(f"[DEBUG] 原始图像数据类型: {original_image.dtype}")
    elif original_image_path:
        if debug:
            print(f"[DEBUG] 警告: 原始图像文件不存在: {original_image_path}")
    
    if debug:
        print(f"[DEBUG] 数据加载完成")
    
    return segments_info, superpixel_mask, original_image

def generate_distinct_colors(n_colors):
    """Generate distinct colors using predefined color map"""
    # Predefined color map for semantic segmentation
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }
    
    # Class names mapping
    class_names = {
        0: "noise",
        1: "barrier", 
        2: "bicycle",
        3: "bus",
        4: "car",
        5: "construction_vehicle",
        6: "motorcycle",
        7: "pedestrian",
        8: "traffic_cone",
        9: "trailer",
        10: "truck",
        11: "driveable_surface",
        12: "other_flat",
        13: "sidewalk",
        14: "terrain",
        15: "manmade",
        16: "vegetation"
    }
    
    colors = []
    for i in range(n_colors):
        # Use predefined colors if available, otherwise generate random colors
        if i < len(color_map):
            r, g, b = color_map[i]
            colors.append((r / 255, g / 255, b / 255))
        else:
            print(f'Unknown class: {i}')
            # Fallback to random colors for additional segments
            r = random.random()
            g = random.random()
            b = random.random()
            colors.append((r, g, b))
    
    return colors, class_names

def create_overlay_image(original_image, superpixel_mask, segments_info, alpha=0.6):
    """
    创建叠加图像
    
    Args:
        original_image: 原始图像
        superpixel_mask: superpixel掩码
        segments_info: 分割信息
        alpha: 透明度
    
    Returns:
        overlay_image: 叠加后的图像
    """
    if original_image is None:
        return None
    
    # 创建彩色掩码
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors, class_names = generate_distinct_colors(len(segments_info))
    
    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
        
    
    # 叠加图像
    overlay_image = original_image.copy().astype(np.float32) / 255.0
    mask_region = (superpixel_mask > 0)
    
    overlay_image[mask_region] = (
        alpha * colored_mask[mask_region] + 
        (1 - alpha) * overlay_image[mask_region]
    )
    
    return overlay_image

def visualize_comparison(segments_info, superpixel_mask, original_image=None,
                        point_gt_data=None, camera_name=None, save_path=None):
    """
    可视化对比 - 显示colored superpixel和原始RGB图像，可选添加点云GT

    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        original_image: 原始图像
        point_gt_data: 点云GT数据字典（包含lidar_points, semantic_mask, camera_matrices）
        camera_name: 相机名称（用于获取对应的变换矩阵）
        save_path: 保存路径
    """
    # 决定显示的图像数量
    n_plots = 0
    if original_image is not None:
        n_plots += 1
    if point_gt_data is not None and camera_name is not None:
        n_plots += 1
    n_plots += 1  # colored superpixels always shown

    if n_plots == 1:
        fig, ax1 = plt.subplots(1, 1, figsize=(6, 6))
        axes = [ax1]
    elif n_plots == 2:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        axes = [ax1, ax2]
    else:  # n_plots == 3
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        axes = [ax1, ax2, ax3]

    current_ax = 0

    # 第一个图：彩色superpixel可视化
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors, class_names = generate_distinct_colors(len(segments_info))

    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
        print(f"Processing segment {i} of {len(segments_info)}...")

    axes[current_ax].imshow(colored_mask)
    axes[current_ax].set_title('Colored Superpixels', fontsize=14)
    axes[current_ax].axis('off')
    current_ax += 1

    # 第二个图：原始RGB图像（如果有的话）
    if original_image is not None:
        axes[current_ax].imshow(original_image)
        axes[current_ax].set_title('Original RGB Image', fontsize=14)
        axes[current_ax].axis('off')
        current_ax += 1

    # 第三个图：带点云GT的RGB图像（如果有的话）
    if point_gt_data is not None and camera_name is not None and original_image is not None:
        if camera_name in point_gt_data['camera_matrices']:
            print(f"Adding point GT for camera: {camera_name}")
            image_with_gt = add_point_gt_to_image(
                original_image,
                point_gt_data['lidar_points'],
                point_gt_data['semantic_mask'],
                point_gt_data['camera_matrices'][camera_name],
                dot_size=2,
                alpha=0.6
            )
            axes[current_ax].imshow(image_with_gt)
            axes[current_ax].set_title('RGB Image + Point GT', fontsize=14)
            axes[current_ax].axis('off')
        else:
            print(f"Warning: Camera {camera_name} not found in camera matrices")

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"对比可视化结果已保存到: {save_path}")

    plt.show()

def create_detailed_analysis(segments_info, superpixel_mask, save_path=None):
    """
    创建详细分析图
    
    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        save_path: 保存路径
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 左上：面积分布直方图
    areas = [seg['area'] for seg in segments_info]
    ax1.hist(areas, bins=min(20, len(areas)), alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('Area (pixels)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Superpixel Area Distribution')
    ax1.grid(True, alpha=0.3)
    
    # 右上：类别分布饼图
    category_counts = {}
    for seg in segments_info:
        cat_id = seg['category_id']
        category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
    
    if len(category_counts) <= 10:  # 只显示前10个类别
        labels = [f'Category {cat_id}' for cat_id in sorted(category_counts.keys())]
        sizes = list(category_counts.values())
        ax2.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Category Distribution')
    else:
        # 如果类别太多，显示条形图
        top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        cat_ids = [str(item[0]) for item in top_categories]
        counts = [item[1] for item in top_categories]
        ax2.bar(cat_ids, counts, color='lightcoral')
        ax2.set_xlabel('Category ID')
        ax2.set_ylabel('Count')
        ax2.set_title('Top 10 Category Distribution')
        ax2.tick_params(axis='x', rotation=45)
    
    # 左下：物体/非物体统计
    thing_count = sum(1 for seg in segments_info if seg['isthing'])
    stuff_count = len(segments_info) - thing_count
    
    labels = ['Thing Classes', 'Stuff Classes']
    sizes = [thing_count, stuff_count]
    colors = ['lightblue', 'lightgreen']
    
    ax3.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    ax3.set_title('Thing Classes vs Stuff Classes')
    
    # 右下：统计信息表格
    ax4.axis('off')
    info_text = f"""
    Statistical Summary:
    
    Total Count: {len(segments_info)}
    Mask Size: {superpixel_mask.shape[1]} × {superpixel_mask.shape[0]}
    Total Area: {sum(areas):,.0f} pixels
    
    Area Statistics:
    Min: {min(areas):,.0f}
    Max: {max(areas):,.0f}
    Mean: {np.mean(areas):,.0f}
    Median: {np.median(areas):,.0f}
    Std: {np.std(areas):,.0f}
    
    Category Count: {len(category_counts)}
    Thing Classes: {thing_count}
    Stuff Classes: {stuff_count}
    """
    
    ax4.text(0.1, 0.9, info_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"详细分析结果已保存到: {save_path}")
    
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='SEEM Superpixel 对比可视化工具')
    parser.add_argument('--json_path', type=str, required=True,
                       help='JSON文件路径')
    parser.add_argument('--png_path', type=str, required=True,
                       help='PNG文件路径')
    parser.add_argument('--original_image', type=str, default=None,
                       help='原始图像路径（可选，如果提供则不会自动查找）')
    parser.add_argument('--nuscenes_dataroot', type=str, default=None,
                       help='NuScenes数据集根目录（用于自动查找原始图像）')
    parser.add_argument('--pkl_path', type=str, default=None,
                       help='包含点云数据信息的pickle文件路径（用于点云GT可视化）')
    parser.add_argument('--camera_name', type=str, default='CAM_FRONT',
                       help='相机名称（用于点云投影，默认为CAM_FRONT）')
    parser.add_argument('--add_point_gt', action='store_true',
                       help='是否添加点云GT可视化')
    parser.add_argument('--output_dir', type=str, default='./output',
                       help='输出目录')
    parser.add_argument('--analysis_only', action='store_true',
                       help='只显示分析图表，不显示对比可视化')
    parser.add_argument('--do_analysis', action='store_true',
                       help='是否生成详细分析图表（默认为False）')
    parser.add_argument('--debug', action='store_true',
                       help='启用debug模式，显示详细信息')
    
    args = parser.parse_args()

    # 检查文件是否存在
    if not os.path.exists(args.json_path):
        print(f"错误: JSON文件不存在: {args.json_path}")
        return

    if not os.path.exists(args.png_path):
        print(f"错误: PNG文件不存在: {args.png_path}")
        return

    # 处理原始图像路径
    original_image_path = args.original_image

    # 如果没有手动指定原始图像路径，尝试通过token自动查找
    if not original_image_path and args.nuscenes_dataroot and os.path.exists(args.nuscenes_dataroot):
        # 从文件名中提取token
        token = Path(args.json_path).stem
        if args.debug:
            print(f"[DEBUG] 从文件名提取token: {token}")
            

        if args.debug:
            print(f"[DEBUG] 正在初始化NuScenes，数据根目录: {args.nuscenes_dataroot}")

        # 初始化NuScenes
        nusc = NuScenes(version="v1.0-trainval", dataroot=args.nuscenes_dataroot, verbose=False)


        print("正在通过token自动查找原始图像...")
        original_image_path = find_original_image_path(nusc, token, args.debug)

        if original_image_path:
            print(f"找到原始图像: {original_image_path}")
        else:
            print("未找到对应的原始图像")

    # 验证原始图像路径
    if original_image_path and not os.path.exists(original_image_path):
        print(f"警告: 原始图像文件不存在: {original_image_path}")
        original_image_path = None
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载数据
    print("正在加载数据...")
    segments_info, superpixel_mask, original_image = load_data(
        args.json_path, args.png_path, original_image_path, args.debug
    )

    # 加载点云GT数据（如果需要）
    point_gt_data = None
    if args.add_point_gt and args.pkl_path and args.nuscenes_dataroot:
        if not os.path.exists(args.pkl_path):
            print(f"警告: Pickle文件不存在: {args.pkl_path}")
        else:
            # 从文件名中提取token
            token = Path(args.json_path).stem
            sample_data = nusc.get('sample_data', token)
            sample_token = nusc.get('sample', sample_data['sample_token'])
            point_gt_data = load_point_cloud_data(
                args.pkl_path, args.nuscenes_dataroot, sample_token, args.debug
            )
            if point_gt_data is None:
                print(f"警告: 无法加载token {sample_token} 的点云GT数据")
            else:
                print(f"成功加载点云GT数据")
    elif args.add_point_gt:
        print("警告: 要添加点云GT，需要同时提供 --pkl_path 和 --nuscenes_dataroot 参数")
    
    # 生成文件名
    base_name = Path(args.json_path).stem
    
    if not args.analysis_only:
        # 对比可视化
        print("正在生成对比可视化...")
        comparison_path = os.path.join(args.output_dir, f"{base_name}_comparison.png")
        visualize_comparison(
            segments_info, superpixel_mask, original_image,
            point_gt_data, args.camera_name, comparison_path
        )

    # 详细分析（仅在启用时生成）
    if args.do_analysis:
        print("正在生成详细分析...")
        analysis_path = os.path.join(args.output_dir, f"{base_name}_analysis.png")
        create_detailed_analysis(segments_info, superpixel_mask, analysis_path)
    
    print("可视化完成!")

if __name__ == "__main__":
    main()
