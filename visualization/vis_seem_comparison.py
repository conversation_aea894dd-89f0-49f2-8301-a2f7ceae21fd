#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SEEM Superpixel 对比可视化工具
显示原始图像和superpixel标签的对比
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import argparse
from pathlib import Path
import random
import cv2

# 设置英文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

def load_data(json_path, png_path, original_image_path=None, debug=False):
    """
    加载所有数据
    
    Args:
        json_path: JSON文件路径
        png_path: PNG文件路径
        original_image_path: 原始图像路径（可选）
        debug: 是否启用debug模式
    
    Returns:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        original_image: 原始图像（如果提供）
    """
    if debug:
        print(f"[DEBUG] 开始加载数据...")
        print(f"[DEBUG] JSON文件: {json_path}")
        print(f"[DEBUG] PNG文件: {png_path}")
        if original_image_path:
            print(f"[DEBUG] 原始图像: {original_image_path}")
    
    # 加载JSON数据
    with open(json_path, 'r') as f:
        segments_info = json.load(f)
    
    if debug:
        print(f"[DEBUG] JSON数据加载完成，包含 {len(segments_info)} 个segment")
        print(f"[DEBUG] 第一个segment示例: {segments_info[0] if segments_info else 'None'}")
    
    # 加载PNG掩码
    superpixel_mask = np.array(Image.open(png_path))
    
    if debug:
        print(f"[DEBUG] PNG掩码加载完成，尺寸: {superpixel_mask.shape}")
        print(f"[DEBUG] 掩码数据类型: {superpixel_mask.dtype}")
        print(f"[DEBUG] 掩码值范围: {superpixel_mask.min()} - {superpixel_mask.max()}")
    
    # 加载原始图像（如果提供）
    original_image = None
    if original_image_path and os.path.exists(original_image_path):
        original_image = cv2.imread(original_image_path)
        original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        
        if debug:
            print(f"[DEBUG] 原始图像加载完成，尺寸: {original_image.shape}")
            print(f"[DEBUG] 原始图像数据类型: {original_image.dtype}")
    elif original_image_path:
        if debug:
            print(f"[DEBUG] 警告: 原始图像文件不存在: {original_image_path}")
    
    if debug:
        print(f"[DEBUG] 数据加载完成")
    
    return segments_info, superpixel_mask, original_image

def generate_distinct_colors(n_colors):
    """Generate distinct colors using predefined color map"""
    # Predefined color map for semantic segmentation
    color_map = {
        0: [0, 0, 0],  # noise                 black
        1: [255, 120, 50],  # barrier               orange
        2: [255, 192, 203],  # bicycle               pink
        3: [255, 255, 0],  # bus                   yellow
        4: [0, 150, 245],  # car                   blue
        5: [0, 255, 255],  # construction_vehicle  cyan
        6: [255, 127, 0],  # motorcycle            dark orange
        7: [255, 0, 0],  # pedestrian            red
        8: [255, 240, 150],  # traffic_cone          light yellow
        9: [135, 60, 0],  # trailer               brown
        10: [160, 32, 240],  # truck                 purple
        11: [255, 0, 255],  # driveable_surface     dark pink
        12: [139, 137, 137],  # other_flat            dark red
        13: [75, 0, 75],  # sidewalk              dark purple
        14: [150, 240, 80],  # terrain               light green
        15: [230, 230, 250],  # manmade               white
        16: [0, 175, 0],  # vegetation            green
    }
    
    # Class names mapping
    class_names = {
        0: "noise",
        1: "barrier", 
        2: "bicycle",
        3: "bus",
        4: "car",
        5: "construction_vehicle",
        6: "motorcycle",
        7: "pedestrian",
        8: "traffic_cone",
        9: "trailer",
        10: "truck",
        11: "driveable_surface",
        12: "other_flat",
        13: "sidewalk",
        14: "terrain",
        15: "manmade",
        16: "vegetation"
    }
    
    colors = []
    for i in range(n_colors):
        # Use predefined colors if available, otherwise generate random colors
        if i < len(color_map):
            r, g, b = color_map[i]
            colors.append((r / 255, g / 255, b / 255))
        else:
            print(f'Unknown class: {i}')
            # Fallback to random colors for additional segments
            r = random.random()
            g = random.random()
            b = random.random()
            colors.append((r, g, b))
    
    return colors, class_names

def create_overlay_image(original_image, superpixel_mask, segments_info, alpha=0.6):
    """
    创建叠加图像
    
    Args:
        original_image: 原始图像
        superpixel_mask: superpixel掩码
        segments_info: 分割信息
        alpha: 透明度
    
    Returns:
        overlay_image: 叠加后的图像
    """
    if original_image is None:
        return None
    
    # 创建彩色掩码
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors, class_names = generate_distinct_colors(len(segments_info))
    
    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
        
    
    # 叠加图像
    overlay_image = original_image.copy().astype(np.float32) / 255.0
    mask_region = (superpixel_mask > 0)
    
    overlay_image[mask_region] = (
        alpha * colored_mask[mask_region] + 
        (1 - alpha) * overlay_image[mask_region]
    )
    
    return overlay_image

def visualize_comparison(segments_info, superpixel_mask, original_image=None, save_path=None):
    """
    可视化对比 - 显示colored superpixel和原始RGB图像

    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        original_image: 原始图像
        save_path: 保存路径
    """
    # 根据是否有原始图像决定显示的图像数量
    if original_image is not None:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    else:
        fig, ax1 = plt.subplots(1, 1, figsize=(6, 6))
        ax2 = None

    # 第一个图：彩色superpixel可视化
    colored_mask = np.zeros((*superpixel_mask.shape, 3))
    colors, class_names = generate_distinct_colors(len(segments_info))

    for i, segment in enumerate(segments_info):
        segment_id = segment['id']
        mask = (superpixel_mask == segment_id)
        colored_mask[mask] = colors[i]
        print(f"Processing segment {i} of {len(segments_info)}...")

    ax1.imshow(colored_mask)
    ax1.set_title('Colored Superpixels', fontsize=14)
    ax1.axis('off')

    # 第二个图：原始RGB图像（如果有的话）
    if original_image is not None and ax2 is not None:
        ax2.imshow(original_image)
        ax2.set_title('Original RGB Image', fontsize=14)
        ax2.axis('off')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"对比可视化结果已保存到: {save_path}")

    plt.show()

def create_detailed_analysis(segments_info, superpixel_mask, save_path=None):
    """
    创建详细分析图
    
    Args:
        segments_info: 分割信息列表
        superpixel_mask: superpixel掩码图像
        save_path: 保存路径
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 左上：面积分布直方图
    areas = [seg['area'] for seg in segments_info]
    ax1.hist(areas, bins=min(20, len(areas)), alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('Area (pixels)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Superpixel Area Distribution')
    ax1.grid(True, alpha=0.3)
    
    # 右上：类别分布饼图
    category_counts = {}
    for seg in segments_info:
        cat_id = seg['category_id']
        category_counts[cat_id] = category_counts.get(cat_id, 0) + 1
    
    if len(category_counts) <= 10:  # 只显示前10个类别
        labels = [f'Category {cat_id}' for cat_id in sorted(category_counts.keys())]
        sizes = list(category_counts.values())
        ax2.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax2.set_title('Category Distribution')
    else:
        # 如果类别太多，显示条形图
        top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        cat_ids = [str(item[0]) for item in top_categories]
        counts = [item[1] for item in top_categories]
        ax2.bar(cat_ids, counts, color='lightcoral')
        ax2.set_xlabel('Category ID')
        ax2.set_ylabel('Count')
        ax2.set_title('Top 10 Category Distribution')
        ax2.tick_params(axis='x', rotation=45)
    
    # 左下：物体/非物体统计
    thing_count = sum(1 for seg in segments_info if seg['isthing'])
    stuff_count = len(segments_info) - thing_count
    
    labels = ['Thing Classes', 'Stuff Classes']
    sizes = [thing_count, stuff_count]
    colors = ['lightblue', 'lightgreen']
    
    ax3.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    ax3.set_title('Thing Classes vs Stuff Classes')
    
    # 右下：统计信息表格
    ax4.axis('off')
    info_text = f"""
    Statistical Summary:
    
    Total Count: {len(segments_info)}
    Mask Size: {superpixel_mask.shape[1]} × {superpixel_mask.shape[0]}
    Total Area: {sum(areas):,.0f} pixels
    
    Area Statistics:
    Min: {min(areas):,.0f}
    Max: {max(areas):,.0f}
    Mean: {np.mean(areas):,.0f}
    Median: {np.median(areas):,.0f}
    Std: {np.std(areas):,.0f}
    
    Category Count: {len(category_counts)}
    Thing Classes: {thing_count}
    Stuff Classes: {stuff_count}
    """
    
    ax4.text(0.1, 0.9, info_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"详细分析结果已保存到: {save_path}")
    
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='SEEM Superpixel 对比可视化工具')
    parser.add_argument('--json_path', type=str, required=True,
                       help='JSON文件路径')
    parser.add_argument('--png_path', type=str, required=True,
                       help='PNG文件路径')
    parser.add_argument('--original_image', type=str, default=None,
                       help='原始图像路径（可选）')
    parser.add_argument('--output_dir', type=str, default='./output',
                       help='输出目录')
    parser.add_argument('--analysis_only', action='store_true',
                       help='只显示分析图表，不显示对比可视化')
    parser.add_argument('--debug', action='store_true',
                       help='启用debug模式，显示详细信息')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.json_path):
        print(f"错误: JSON文件不存在: {args.json_path}")
        return
    
    if not os.path.exists(args.png_path):
        print(f"错误: PNG文件不存在: {args.png_path}")
        return
    
    if args.original_image and not os.path.exists(args.original_image):
        print(f"警告: 原始图像文件不存在: {args.original_image}")
        args.original_image = None
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载数据
    print("正在加载数据...")
    segments_info, superpixel_mask, original_image = load_data(
        args.json_path, args.png_path, args.original_image
    )
    
    # 生成文件名
    base_name = Path(args.json_path).stem
    
    if not args.analysis_only:
        # 对比可视化
        print("正在生成对比可视化...")
        comparison_path = os.path.join(args.output_dir, f"{base_name}_comparison.png")
        visualize_comparison(segments_info, superpixel_mask, original_image, comparison_path)
    
    # 详细分析
    print("正在生成详细分析...")
    analysis_path = os.path.join(args.output_dir, f"{base_name}_analysis.png")
    create_detailed_analysis(segments_info, superpixel_mask, analysis_path)
    
    print("可视化完成!")

if __name__ == "__main__":
    main()
