#!/bin/bash

# 批量可视化配置
INDEX=0  # 要可视化的数据索引
PKL_PATH="generation/SEEM/demo_code/data/sets/nuscenes/nuscenes_infos_train.pkl"
DATA_ROOT="generation/SEEM/demo_code/data/sets/nuscenes"
SUPERPIXEL_DIR="generation/SEEM/demo_code/superpixels/nuscenes/superpixels_seem"
CAMERA_NAME="CAM_FRONT"  # 可选: CAM_FRONT, CAM_FRONT_RIGHT, CAM_FRONT_LEFT, CAM_BACK, CAM_BACK_LEFT, CAM_BACK_RIGHT

# 运行批量可视化
python visualization/vis_seem_batch.py \
--pkl_path \
${PKL_PATH} \
--data_root \
${DATA_ROOT} \
--superpixel_dir \
${SUPERPIXEL_DIR} \
--index \
${INDEX} \
--camera_name \
${CAMERA_NAME} \
--add_point_gt \
--output_dir \
./batch_visualization_output \
--debug

echo "批量可视化完成！"
echo "输出目录: ./batch_visualization_output"
echo "处理的数据索引: ${INDEX}"
echo "使用的相机: ${CAMERA_NAME}"
