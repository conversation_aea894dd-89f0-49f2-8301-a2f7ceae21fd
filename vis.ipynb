import numpy as np

panoptic_path = 'generation/SEEM/demo_code/data/sets/nuscenes/panoptic/v1.0-trainval/69b793ec8dc44e2fbd33d8cdd16b5a31_panoptic.npz'
panoptic = np.load(panoptic_path, allow_pickle=True)
print(panoptic)

def load_mask(pts_panoptic_mask_path, backend_args=None, seg_3d_dtype=np.int64, seg_offset=1000):
    pts_panoptic_mask = np.load(pts_panoptic_mask_path) 
    
    pts_panoptic_mask = pts_panoptic_mask['data']
    pts_semantic_mask = pts_panoptic_mask // seg_offset
    pts_instance_mask = pts_panoptic_mask.astype(seg_3d_dtype)
    return pts_semantic_mask, pts_instance_mask

sem, ins = load_mask(panoptic_path)
print(sem)
print(ins)

